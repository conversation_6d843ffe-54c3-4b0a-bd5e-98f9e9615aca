import React, { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Tooltip } from "recharts";
import { getAllocation } from "../services/api";

const AllocationCharts = () => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    getAllocation()
      .then((res) => {
        setData(res.data);
        setLoading(false);
      })
      .catch((err) => {
        setError(err.message);
        setLoading(false);
      });
  }, []);

  const COLORS = {
    'Technology': '#3b82f6',
    'Healthcare': '#10b981',
    'Financial': '#f59e0b',
    'Consumer Staples': '#8b5cf6',
    'Energy': '#ef4444',
    'Industrials': '#06b6d4'
  };

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  if (loading) return (
    <div style={{
      padding: "3rem",
      textAlign: "center",
      color: "#64748b",
      fontSize: "1.125rem"
    }}>
      Loading allocation data...
    </div>
  );

  if (error) return (
    <div style={{
      padding: "2rem",
      textAlign: "center",
      color: "#ef4444",
      fontSize: "1.125rem",
      backgroundColor: "#fef2f2",
      borderRadius: "8px",
      border: "1px solid #fecaca"
    }}>
      Error loading allocation: {error}
    </div>
  );

  if (!data) return (
    <div style={{
      padding: "3rem",
      textAlign: "center",
      color: "#64748b",
      fontSize: "1.125rem"
    }}>
      No allocation data available
    </div>
  );

  const CustomTooltip = ({ active, payload }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div style={{
          backgroundColor: "#ffffff",
          padding: "1rem",
          borderRadius: "8px",
          boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
          border: "1px solid #e2e8f0"
        }}>
          <p style={{
            margin: "0 0 0.5rem 0",
            fontWeight: "600",
            color: "#1e293b"
          }}>
            {data.sector}
          </p>
          <p style={{
            margin: "0",
            color: "#64748b",
            fontSize: "0.875rem"
          }}>
            Value: {formatCurrency(data.value)}
          </p>
          <p style={{
            margin: "0",
            color: "#64748b",
            fontSize: "0.875rem"
          }}>
            Percentage: {data.percentage}%
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <div style={{
      display: "grid",
      gridTemplateColumns: "1fr 1fr",
      gap: "3rem",
      alignItems: "start"
    }}>
      {/* Pie Chart */}
      <div style={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center"
      }}>
        <h3 style={{
          fontSize: "1.125rem",
          fontWeight: "600",
          color: "#374151",
          margin: "0 0 2rem 0",
          textAlign: "center"
        }}>
          Portfolio Distribution
        </h3>
        <ResponsiveContainer width="100%" height={350}>
          <PieChart>
            <Pie
              data={data.allocation}
              cx="50%"
              cy="50%"
              innerRadius={60}
              outerRadius={120}
              paddingAngle={2}
              dataKey="value"
            >
              {data.allocation.map((entry, index) => (
                <Cell
                  key={`cell-${index}`}
                  fill={COLORS[entry.sector] || '#64748b'}
                  stroke="#ffffff"
                  strokeWidth={2}
                />
              ))}
            </Pie>
            <Tooltip content={<CustomTooltip />} />
          </PieChart>
        </ResponsiveContainer>
      </div>

      {/* Sector Breakdown */}
      <div>
        <h3 style={{
          fontSize: "1.125rem",
          fontWeight: "600",
          color: "#374151",
          margin: "0 0 1.5rem 0"
        }}>
          Sector Breakdown
        </h3>
        <div style={{
          display: "flex",
          flexDirection: "column",
          gap: "1rem"
        }}>
          {data.allocation
            .sort((a, b) => b.value - a.value)
            .map((sector, index) => (
            <div
              key={index}
              style={{
                padding: "1.25rem",
                backgroundColor: "#f8fafc",
                borderRadius: "8px",
                border: "1px solid #e2e8f0",
                transition: "all 0.2s ease"
              }}
            >
              <div style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                marginBottom: "0.75rem"
              }}>
                <div style={{
                  display: "flex",
                  alignItems: "center",
                  gap: "0.75rem"
                }}>
                  <div style={{
                    width: "12px",
                    height: "12px",
                    borderRadius: "3px",
                    backgroundColor: COLORS[sector.sector] || '#64748b'
                  }}></div>
                  <span style={{
                    fontWeight: "600",
                    color: "#374151",
                    fontSize: "0.875rem"
                  }}>
                    {sector.sector}
                  </span>
                </div>
                <span style={{
                  fontWeight: "700",
                  color: "#1e293b",
                  fontSize: "1rem"
                }}>
                  {sector.percentage}%
                </span>
              </div>

              <div style={{
                width: "100%",
                height: "6px",
                backgroundColor: "#e2e8f0",
                borderRadius: "3px",
                overflow: "hidden",
                marginBottom: "0.5rem"
              }}>
                <div style={{
                  width: `${sector.percentage}%`,
                  height: "100%",
                  backgroundColor: COLORS[sector.sector] || '#64748b',
                  borderRadius: "3px",
                  transition: "width 0.3s ease"
                }}></div>
              </div>

              <div style={{
                fontSize: "0.875rem",
                color: "#64748b",
                fontWeight: "500"
              }}>
                {formatCurrency(sector.value)}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default AllocationCharts;
                  <td style={{ padding: "8px", textAlign: "right" }}>{sector.percentage}%</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default AllocationCharts;
