import React, { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON>, Pie, Cell, Responsive<PERSON><PERSON><PERSON>, <PERSON>, Tooltip } from "recharts";
import { getAllocation } from "../services/api";

const AllocationCharts = () => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    getAllocation()
      .then((res) => {
        setData(res.data);
        setLoading(false);
      })
      .catch((err) => {
        setError(err.message);
        setLoading(false);
      });
  }, []);

  const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884D8", "#82CA9D"];

  if (loading) return <p>Loading allocation data...</p>;
  if (error) return <p>Error: {error}</p>;
  if (!data) return <p>No data available</p>;

  return (
    <div className="allocation-charts">
      <div style={{ display: "flex", gap: "2rem", flexWrap: "wrap" }}>
        {/* Pie Chart */}
        <div style={{ flex: "1", minWidth: "400px" }}>
          <h3>Sector Allocation</h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={data.allocation}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ sector, percentage }) => `${sector} (${percentage}%)`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {data.allocation.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip 
                formatter={(value) => [`$${value.toFixed(2)}`, "Value"]}
                labelFormatter={(label) => `Sector: ${label}`}
              />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </div>

        {/* Sector Breakdown Table */}
        <div style={{ flex: "1", minWidth: "300px" }}>
          <h3>Sector Breakdown</h3>
          <table style={{ width: "100%", borderCollapse: "collapse" }}>
            <thead>
              <tr style={{ backgroundColor: "#f5f5f5" }}>
                <th style={{ padding: "8px", textAlign: "left", borderBottom: "1px solid #ddd" }}>Sector</th>
                <th style={{ padding: "8px", textAlign: "right", borderBottom: "1px solid #ddd" }}>Value</th>
                <th style={{ padding: "8px", textAlign: "right", borderBottom: "1px solid #ddd" }}>Percentage</th>
              </tr>
            </thead>
            <tbody>
              {data.allocation.map((sector, index) => (
                <tr key={index} style={{ borderBottom: "1px solid #eee" }}>
                  <td style={{ padding: "8px" }}>{sector.sector}</td>
                  <td style={{ padding: "8px", textAlign: "right" }}>${sector.value.toFixed(2)}</td>
                  <td style={{ padding: "8px", textAlign: "right" }}>{sector.percentage}%</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default AllocationCharts;
