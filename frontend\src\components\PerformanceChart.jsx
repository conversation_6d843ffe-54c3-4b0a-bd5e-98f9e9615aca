import React, { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>xis, <PERSON><PERSON><PERSON>s, CartesianGrid, Tooltip, Responsive<PERSON><PERSON><PERSON>, <PERSON> } from "recharts";
import { getPerformance } from "../services/api";

const PerformanceChart = () => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    getPerformance()
      .then((res) => {
        setData(res.data);
        setLoading(false);
      })
      .catch((err) => {
        setError(err.message);
        setLoading(false);
      });
  }, []);

  if (loading) return <p>Loading performance data...</p>;
  if (error) return <p>Error: {error}</p>;
  if (!data) return <p>No data available</p>;

  return (
    <div className="performance-chart">
      <div style={{ display: "flex", gap: "2rem", flexWrap: "wrap" }}>
        {/* Performance Bar Chart */}
        <div style={{ flex: "1", minWidth: "500px" }}>
          <h3>Performance by Stock</h3>
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={data.performance}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="symbol" />
              <YAxis />
              <Tooltip 
                formatter={(value, name) => [
                  name === "gainLossPercent" ? `${value}%` : `$${value.toFixed(2)}`,
                  name === "gainLossPercent" ? "Gain/Loss %" : "Gain/Loss $"
                ]}
                labelFormatter={(label) => `Stock: ${label}`}
              />
              <Legend />
              <Bar 
                dataKey="gainLossPercent" 
                fill="#8884d8" 
                name="Gain/Loss %"
              />
            </BarChart>
          </ResponsiveContainer>
        </div>

        {/* Performance Table */}
        <div style={{ flex: "1", minWidth: "300px" }}>
          <h3>Performance Summary</h3>
          <table style={{ width: "100%", borderCollapse: "collapse" }}>
            <thead>
              <tr style={{ backgroundColor: "#f5f5f5" }}>
                <th style={{ padding: "8px", textAlign: "left", borderBottom: "1px solid #ddd" }}>Stock</th>
                <th style={{ padding: "8px", textAlign: "right", borderBottom: "1px solid #ddd" }}>Gain/Loss</th>
                <th style={{ padding: "8px", textAlign: "right", borderBottom: "1px solid #ddd" }}>%</th>
                <th style={{ padding: "8px", textAlign: "right", borderBottom: "1px solid #ddd" }}>Value</th>
              </tr>
            </thead>
            <tbody>
              {data.performance.map((stock, index) => (
                <tr key={index} style={{ borderBottom: "1px solid #eee" }}>
                  <td style={{ padding: "8px", fontWeight: "bold" }}>{stock.symbol}</td>
                  <td 
                    style={{ 
                      padding: "8px", 
                      textAlign: "right", 
                      color: stock.gainLoss >= 0 ? "green" : "red",
                      fontWeight: "bold"
                    }}
                  >
                    ${stock.gainLoss.toFixed(2)}
                  </td>
                  <td 
                    style={{ 
                      padding: "8px", 
                      textAlign: "right", 
                      color: stock.gainLossPercent >= 0 ? "green" : "red"
                    }}
                  >
                    {stock.gainLossPercent >= 0 ? "+" : ""}{stock.gainLossPercent.toFixed(2)}%
                  </td>
                  <td style={{ padding: "8px", textAlign: "right" }}>${stock.marketValue.toFixed(2)}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default PerformanceChart;
