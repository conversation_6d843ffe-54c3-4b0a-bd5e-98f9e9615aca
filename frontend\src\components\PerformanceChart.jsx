import React, { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, <PERSON>Axis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts";
import { getPerformance } from "../services/api";

const PerformanceChart = () => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    getPerformance()
      .then((res) => {
        setData(res.data);
        setLoading(false);
      })
      .catch((err) => {
        setError(err.message);
        setLoading(false);
      });
  }, []);

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(value);
  };

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div style={{
          backgroundColor: "#ffffff",
          padding: "1rem",
          borderRadius: "8px",
          boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
          border: "1px solid #e2e8f0"
        }}>
          <p style={{
            margin: "0 0 0.5rem 0",
            fontWeight: "600",
            color: "#1e293b"
          }}>
            {data.symbol} - {data.name}
          </p>
          <p style={{
            margin: "0 0 0.25rem 0",
            color: data.gainLoss >= 0 ? "#10b981" : "#ef4444",
            fontWeight: "600"
          }}>
            Gain/Loss: {formatCurrency(data.gainLoss)}
          </p>
          <p style={{
            margin: "0 0 0.25rem 0",
            color: data.gainLossPercent >= 0 ? "#10b981" : "#ef4444",
            fontWeight: "600"
          }}>
            Percentage: {data.gainLossPercent >= 0 ? "+" : ""}{data.gainLossPercent.toFixed(2)}%
          </p>
          <p style={{
            margin: "0",
            color: "#64748b",
            fontSize: "0.875rem"
          }}>
            Market Value: {formatCurrency(data.marketValue)}
          </p>
        </div>
      );
    }
    return null;
  };

  if (loading) return (
    <div style={{
      padding: "3rem",
      textAlign: "center",
      color: "#64748b",
      fontSize: "1.125rem"
    }}>
      Loading performance data...
    </div>
  );

  if (error) return (
    <div style={{
      padding: "2rem",
      textAlign: "center",
      color: "#ef4444",
      fontSize: "1.125rem",
      backgroundColor: "#fef2f2",
      borderRadius: "8px",
      border: "1px solid #fecaca"
    }}>
      Error loading performance: {error}
    </div>
  );

  if (!data) return (
    <div style={{
      padding: "3rem",
      textAlign: "center",
      color: "#64748b",
      fontSize: "1.125rem"
    }}>
      No performance data available
    </div>
  );

  return (
    <div style={{
      display: "grid",
      gridTemplateColumns: "2fr 1fr",
      gap: "3rem",
      alignItems: "start"
    }}>
      {/* Performance Bar Chart */}
      <div>
        <h3 style={{
          fontSize: "1.125rem",
          fontWeight: "600",
          color: "#374151",
          margin: "0 0 2rem 0"
        }}>
          Performance Comparison
        </h3>
        <div style={{
          backgroundColor: "#f8fafc",
          borderRadius: "8px",
          padding: "1.5rem",
          border: "1px solid #e2e8f0"
        }}>
          <ResponsiveContainer width="100%" height={400}>
            <BarChart
              data={data.performance}
              margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid
                strokeDasharray="3 3"
                stroke="#e2e8f0"
                vertical={false}
              />
              <XAxis
                dataKey="symbol"
                axisLine={false}
                tickLine={false}
                tick={{ fill: '#64748b', fontSize: 12, fontWeight: 500 }}
              />
              <YAxis
                axisLine={false}
                tickLine={false}
                tick={{ fill: '#64748b', fontSize: 12 }}
                tickFormatter={(value) => `${value}%`}
              />
              <Tooltip content={<CustomTooltip />} />
              <Bar
                dataKey="gainLossPercent"
                radius={[4, 4, 0, 0]}
                fill={(entry) => entry >= 0 ? "#10b981" : "#ef4444"}
              >
                {data.performance.map((entry, index) => (
                  <Bar
                    key={`bar-${index}`}
                    fill={entry.gainLossPercent >= 0 ? "#10b981" : "#ef4444"}
                  />
                ))}
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Performance Rankings */}
      <div>
        <h3 style={{
          fontSize: "1.125rem",
          fontWeight: "600",
          color: "#374151",
          margin: "0 0 1.5rem 0"
        }}>
          Performance Rankings
        </h3>
        <div style={{
          display: "flex",
          flexDirection: "column",
          gap: "0.75rem"
        }}>
          {data.performance
            .sort((a, b) => b.gainLossPercent - a.gainLossPercent)
            .map((stock, index) => {
              const isPositive = stock.gainLoss >= 0;
              const isTop = index === 0;
              const isBottom = index === data.performance.length - 1;

              return (
                <div
                  key={index}
                  style={{
                    padding: "1rem",
                    backgroundColor: isTop ? "#f0fdf4" : isBottom ? "#fef2f2" : "#ffffff",
                    borderRadius: "8px",
                    border: `1px solid ${isTop ? "#bbf7d0" : isBottom ? "#fecaca" : "#e2e8f0"}`,
                    transition: "all 0.2s ease"
                  }}
                >
                  <div style={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    marginBottom: "0.5rem"
                  }}>
                    <div style={{
                      display: "flex",
                      alignItems: "center",
                      gap: "0.5rem"
                    }}>
                      <span style={{
                        display: "inline-flex",
                        alignItems: "center",
                        justifyContent: "center",
                        width: "24px",
                        height: "24px",
                        borderRadius: "50%",
                        backgroundColor: isTop ? "#10b981" : isBottom ? "#ef4444" : "#64748b",
                        color: "#ffffff",
                        fontSize: "0.75rem",
                        fontWeight: "600"
                      }}>
                        {index + 1}
                      </span>
                      <span style={{
                        fontWeight: "600",
                        color: "#374151",
                        fontSize: "0.875rem"
                      }}>
                        {stock.symbol}
                      </span>
                    </div>
                    <span style={{
                      fontWeight: "700",
                      color: isPositive ? "#10b981" : "#ef4444",
                      fontSize: "1rem"
                    }}>
                      {isPositive ? "+" : ""}{stock.gainLossPercent.toFixed(2)}%
                    </span>
                  </div>

                  <div style={{
                    display: "flex",
                    justifyContent: "space-between",
                    fontSize: "0.75rem",
                    color: "#64748b"
                  }}>
                    <span>
                      {isPositive ? "Gain" : "Loss"}: {formatCurrency(Math.abs(stock.gainLoss))}
                    </span>
                    <span>
                      Value: {formatCurrency(stock.marketValue)}
                    </span>
                  </div>
                </div>
              );
            })}
        </div>
      </div>
    </div>
  );
};

export default PerformanceChart;
