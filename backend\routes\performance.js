const express = require("express");
const router = express.Router();
const holdings = require("../data/holdings.json");

router.get("/", (req, res) => {
  const performanceData = holdings.holdings.map(holding => ({
    symbol: holding.symbol,
    name: holding.name,
    gainLoss: holding.gainLoss,
    gainLossPercent: holding.gainLossPercent,
    marketValue: holding.marketValue
  }));

  // Sort by gain/loss percentage
  performanceData.sort((a, b) => b.gainLossPercent - a.gainLossPercent);

  res.status(200).json({
    performance: performanceData
  });
});

module.exports = router; 