const express = require("express");
const router = express.Router();
const holdings = require("../data/holdings.json");

router.get("/", (req, res) => {
  const totalValue = holdings.holdings.reduce((sum, holding) => sum + holding.marketValue, 0);
  const totalGainLoss = holdings.holdings.reduce((sum, holding) => sum + holding.gainLoss, 0);
  
  // Calculate risk level based on sector diversification
  const sectors = [...new Set(holdings.holdings.map(h => h.sector))];
  const riskLevel = sectors.length >= 4 ? "Low" : sectors.length >= 2 ? "Medium" : "High";

  res.status(200).json({
    totalValue: totalValue.toFixed(2),
    totalGainLoss: totalGainLoss.toFixed(2),
    riskLevel,
    totalHoldings: holdings.holdings.length,
    sectors: sectors.length
  });
});

module.exports = router; 