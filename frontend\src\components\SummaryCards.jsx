import React, { useEffect, useState } from "react";
import { getSummary } from "../services/api";

const SummaryCards = () => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    getSummary()
      .then((res) => {
        setData(res.data);
        setLoading(false);
      })
      .catch((err) => {
        setError(err.message);
        setLoading(false);
      });
  }, []);

  if (loading) return <p>Loading summary...</p>;
  if (error) return <p>Error: {error}</p>;
  if (!data) return <p>No data available</p>;

  return (
    <div className="summary-cards" style={{ 
      display: "grid", 
      gridTemplateColumns: "repeat(auto-fit, minmax(250px, 1fr))", 
      gap: "1rem",
      marginBottom: "2rem"
    }}>
      <div style={{ 
        padding: "1rem", 
        backgroundColor: "#f8f9fa", 
        borderRadius: "8px", 
        border: "1px solid #e9ecef" 
      }}>
        <h3 style={{ margin: "0 0 0.5rem 0", color: "#495057" }}>Total Portfolio Value</h3>
        <p style={{ fontSize: "1.5rem", fontWeight: "bold", margin: "0", color: "#212529" }}>
          ${data.totalValue}
        </p>
      </div>
      
      <div style={{ 
        padding: "1rem", 
        backgroundColor: "#f8f9fa", 
        borderRadius: "8px", 
        border: "1px solid #e9ecef" 
      }}>
        <h3 style={{ margin: "0 0 0.5rem 0", color: "#495057" }}>Total Gain/Loss</h3>
        <p style={{ 
          fontSize: "1.5rem", 
          fontWeight: "bold", 
          margin: "0", 
          color: parseFloat(data.totalGainLoss) >= 0 ? "#28a745" : "#dc3545" 
        }}>
          ${data.totalGainLoss}
        </p>
      </div>
      
      <div style={{ 
        padding: "1rem", 
        backgroundColor: "#f8f9fa", 
        borderRadius: "8px", 
        border: "1px solid #e9ecef" 
      }}>
        <h3 style={{ margin: "0 0 0.5rem 0", color: "#495057" }}>Risk Level</h3>
        <p style={{ 
          fontSize: "1.5rem", 
          fontWeight: "bold", 
          margin: "0",
          color: data.riskLevel === "Low" ? "#28a745" : data.riskLevel === "Medium" ? "#ffc107" : "#dc3545"
        }}>
          {data.riskLevel}
        </p>
      </div>
      
      <div style={{ 
        padding: "1rem", 
        backgroundColor: "#f8f9fa", 
        borderRadius: "8px", 
        border: "1px solid #e9ecef" 
      }}>
        <h3 style={{ margin: "0 0 0.5rem 0", color: "#495057" }}>Holdings</h3>
        <p style={{ fontSize: "1.5rem", fontWeight: "bold", margin: "0", color: "#212529" }}>
          {data.totalHoldings} stocks
        </p>
      </div>
    </div>
  );
};

export default SummaryCards;
