import React, { useEffect, useState } from "react";
import { getSummary } from "../services/api";

const SummaryCards = () => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    getSummary()
      .then((res) => {
        setData(res.data);
        setLoading(false);
      })
      .catch((err) => {
        setError(err.message);
        setLoading(false);
      });
  }, []);

  if (loading) return (
    <div style={{
      padding: "2rem",
      textAlign: "center",
      color: "#64748b",
      fontSize: "1.125rem"
    }}>
      Loading portfolio summary...
    </div>
  );

  if (error) return (
    <div style={{
      padding: "2rem",
      textAlign: "center",
      color: "#ef4444",
      fontSize: "1.125rem",
      backgroundColor: "#fef2f2",
      borderRadius: "8px",
      border: "1px solid #fecaca"
    }}>
      Error loading data: {error}
    </div>
  );

  if (!data) return (
    <div style={{
      padding: "2rem",
      textAlign: "center",
      color: "#64748b",
      fontSize: "1.125rem"
    }}>
      No data available
    </div>
  );

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(value);
  };

  const gainLossValue = parseFloat(data.totalGainLoss);
  const isPositive = gainLossValue >= 0;

  return (
    <div style={{
      display: "grid",
      gridTemplateColumns: "repeat(auto-fit, minmax(280px, 1fr))",
      gap: "1.5rem"
    }}>
      {/* Total Portfolio Value Card */}
      <div style={{
        padding: "2rem",
        backgroundColor: "#ffffff",
        borderRadius: "12px",
        border: "1px solid #e2e8f0",
        boxShadow: "0 1px 3px 0 rgba(0, 0, 0, 0.1)",
        transition: "all 0.2s ease-in-out"
      }}>
        <div style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          marginBottom: "1rem"
        }}>
          <h3 style={{
            margin: "0",
            color: "#64748b",
            fontSize: "0.875rem",
            fontWeight: "500",
            textTransform: "uppercase",
            letterSpacing: "0.05em"
          }}>
            Total Portfolio Value
          </h3>
          <div style={{
            width: "8px",
            height: "8px",
            borderRadius: "50%",
            backgroundColor: "#3b82f6"
          }}></div>
        </div>
        <p style={{
          fontSize: "2.25rem",
          fontWeight: "700",
          margin: "0",
          color: "#1e293b",
          lineHeight: "1"
        }}>
          {formatCurrency(data.totalValue)}
        </p>
      </div>

      {/* Total Gain/Loss Card */}
      <div style={{
        padding: "2rem",
        backgroundColor: "#ffffff",
        borderRadius: "12px",
        border: "1px solid #e2e8f0",
        boxShadow: "0 1px 3px 0 rgba(0, 0, 0, 0.1)",
        transition: "all 0.2s ease-in-out"
      }}>
        <div style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          marginBottom: "1rem"
        }}>
          <h3 style={{
            margin: "0",
            color: "#64748b",
            fontSize: "0.875rem",
            fontWeight: "500",
            textTransform: "uppercase",
            letterSpacing: "0.05em"
          }}>
            Total Gain/Loss
          </h3>
          <div style={{
            width: "8px",
            height: "8px",
            borderRadius: "50%",
            backgroundColor: isPositive ? "#10b981" : "#ef4444"
          }}></div>
        </div>
        <p style={{
          fontSize: "2.25rem",
          fontWeight: "700",
          margin: "0",
          color: isPositive ? "#10b981" : "#ef4444",
          lineHeight: "1"
        }}>
          {isPositive ? "+" : ""}{formatCurrency(data.totalGainLoss)}
        </p>
      </div>

      {/* Risk Level Card */}
      <div style={{
        padding: "2rem",
        backgroundColor: "#ffffff",
        borderRadius: "12px",
        border: "1px solid #e2e8f0",
        boxShadow: "0 1px 3px 0 rgba(0, 0, 0, 0.1)",
        transition: "all 0.2s ease-in-out"
      }}>
        <div style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          marginBottom: "1rem"
        }}>
          <h3 style={{
            margin: "0",
            color: "#64748b",
            fontSize: "0.875rem",
            fontWeight: "500",
            textTransform: "uppercase",
            letterSpacing: "0.05em"
          }}>
            Risk Level
          </h3>
          <div style={{
            width: "8px",
            height: "8px",
            borderRadius: "50%",
            backgroundColor: data.riskLevel === "Low" ? "#10b981" :
                           data.riskLevel === "Medium" ? "#f59e0b" : "#ef4444"
          }}></div>
        </div>
        <p style={{
          fontSize: "2.25rem",
          fontWeight: "700",
          margin: "0",
          color: data.riskLevel === "Low" ? "#10b981" :
                 data.riskLevel === "Medium" ? "#f59e0b" : "#ef4444",
          lineHeight: "1"
        }}>
          {data.riskLevel}
        </p>
        <p style={{
          fontSize: "0.875rem",
          color: "#64748b",
          margin: "0.5rem 0 0 0",
          fontWeight: "400"
        }}>
          Based on {data.sectors} sectors
        </p>
      </div>

      {/* Holdings Card */}
      <div style={{
        padding: "2rem",
        backgroundColor: "#ffffff",
        borderRadius: "12px",
        border: "1px solid #e2e8f0",
        boxShadow: "0 1px 3px 0 rgba(0, 0, 0, 0.1)",
        transition: "all 0.2s ease-in-out"
      }}>
        <div style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          marginBottom: "1rem"
        }}>
          <h3 style={{
            margin: "0",
            color: "#64748b",
            fontSize: "0.875rem",
            fontWeight: "500",
            textTransform: "uppercase",
            letterSpacing: "0.05em"
          }}>
            Total Holdings
          </h3>
          <div style={{
            width: "8px",
            height: "8px",
            borderRadius: "50%",
            backgroundColor: "#8b5cf6"
          }}></div>
        </div>
        <p style={{
          fontSize: "2.25rem",
          fontWeight: "700",
          margin: "0",
          color: "#1e293b",
          lineHeight: "1"
        }}>
          {data.totalHoldings}
        </p>
        <p style={{
          fontSize: "0.875rem",
          color: "#64748b",
          margin: "0.5rem 0 0 0",
          fontWeight: "400"
        }}>
          Individual stocks
        </p>
      </div>
    </div>
  );
};

export default SummaryCards;
