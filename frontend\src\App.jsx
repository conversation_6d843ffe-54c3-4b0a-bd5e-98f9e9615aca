import React from "react";
import SummaryCards from "./components/SummaryCards";
import HoldingsTable from "./components/HoldingsTable";
import AllocationCharts from "./components/AllocationCharts";
import PerformanceChart from "./components/PerformanceChart";

function App() {
  return (
    <div style={{
      minHeight: "100vh",
      backgroundColor: "#f8fafc",
      fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"
    }}>
      {/* Header */}
      <header style={{
        backgroundColor: "#ffffff",
        borderBottom: "1px solid #e2e8f0",
        padding: "2rem 0",
        marginBottom: "2rem",
        boxShadow: "0 1px 3px 0 rgba(0, 0, 0, 0.1)"
      }}>
        <div style={{
          maxWidth: "1200px",
          margin: "0 auto",
          padding: "0 2rem"
        }}>
          <h1 style={{
            fontSize: "2.5rem",
            fontWeight: "700",
            color: "#1e293b",
            margin: "0",
            letterSpacing: "-0.025em"
          }}>
            Portfolio Analytics Dashboard
          </h1>
          <p style={{
            fontSize: "1.125rem",
            color: "#64748b",
            margin: "0.5rem 0 0 0",
            fontWeight: "400"
          }}>
            Track your investments and portfolio performance
          </p>
        </div>
      </header>

      {/* Main Content */}
      <main style={{
        maxWidth: "1200px",
        margin: "0 auto",
        padding: "0 2rem 4rem 2rem"
      }}>
        <section style={{ marginBottom: "3rem" }}>
          <SummaryCards />
        </section>

        <section style={{ marginBottom: "3rem" }}>
          <div style={{
            backgroundColor: "#ffffff",
            borderRadius: "12px",
            padding: "2rem",
            boxShadow: "0 1px 3px 0 rgba(0, 0, 0, 0.1)",
            border: "1px solid #e2e8f0"
          }}>
            <h2 style={{
              fontSize: "1.5rem",
              fontWeight: "600",
              color: "#1e293b",
              margin: "0 0 1.5rem 0",
              letterSpacing: "-0.025em"
            }}>
              Asset Allocation
            </h2>
            <AllocationCharts />
          </div>
        </section>

        <section style={{ marginBottom: "3rem" }}>
          <div style={{
            backgroundColor: "#ffffff",
            borderRadius: "12px",
            padding: "2rem",
            boxShadow: "0 1px 3px 0 rgba(0, 0, 0, 0.1)",
            border: "1px solid #e2e8f0"
          }}>
            <h2 style={{
              fontSize: "1.5rem",
              fontWeight: "600",
              color: "#1e293b",
              margin: "0 0 1.5rem 0",
              letterSpacing: "-0.025em"
            }}>
              Holdings Overview
            </h2>
            <HoldingsTable />
          </div>
        </section>

        <section>
          <div style={{
            backgroundColor: "#ffffff",
            borderRadius: "12px",
            padding: "2rem",
            boxShadow: "0 1px 3px 0 rgba(0, 0, 0, 0.1)",
            border: "1px solid #e2e8f0"
          }}>
            <h2 style={{
              fontSize: "1.5rem",
              fontWeight: "600",
              color: "#1e293b",
              margin: "0 0 1.5rem 0",
              letterSpacing: "-0.025em"
            }}>
              Performance Analysis
            </h2>
            <PerformanceChart />
          </div>
        </section>
      </main>
    </div>
  );
}

export default App;
