import React from "react";
import SummaryCards from "./components/SummaryCards";
import HoldingsTable from "./components/HoldingsTable";
import AllocationCharts from "./components/AllocationCharts";
import PerformanceChart from "./components/PerformanceChart";

function App() {
  return (
    <div style={{ padding: "1rem", fontFamily: "sans-serif" }}>
      <h1>📊 Portfolio Analytics Dashboard</h1>

      <section>
        <SummaryCards />
      </section>

      <section style={{ marginTop: "2rem" }}>
        <h2>Asset Allocation</h2>
        <AllocationCharts />
      </section>

      <section style={{ marginTop: "2rem" }}>
        <h2>Holdings Overview</h2>
        <HoldingsTable />
      </section>

      <section style={{ marginTop: "2rem" }}>
        <h2>Performance Comparison</h2>
        <PerformanceChart />
      </section>
    </div>
  );
}

export default App;
