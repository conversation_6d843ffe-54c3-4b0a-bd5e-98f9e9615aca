import React, { useEffect, useState } from "react";
import { getHoldings } from "../services/api";

const HoldingsTable = () => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    getHoldings()
      .then((res) => {
        setData(res.data);
        setLoading(false);
      })
      .catch((err) => {
        setError(err.message);
        setLoading(false);
      });
  }, []);

  if (loading) return <p>Loading holdings...</p>;
  if (error) return <p>Error: {error}</p>;
  if (!data) return <p>No data available</p>;

  return (
    <div className="holdings-table">
      <table style={{ width: "100%", borderCollapse: "collapse", marginTop: "1rem" }}>
        <thead>
          <tr style={{ backgroundColor: "#f5f5f5" }}>
            <th style={{ padding: "12px", textAlign: "left", borderBottom: "1px solid #ddd" }}>Symbol</th>
            <th style={{ padding: "12px", textAlign: "left", borderBottom: "1px solid #ddd" }}>Name</th>
            <th style={{ padding: "12px", textAlign: "right", borderBottom: "1px solid #ddd" }}>Quantity</th>
            <th style={{ padding: "12px", textAlign: "right", borderBottom: "1px solid #ddd" }}>Avg Price</th>
            <th style={{ padding: "12px", textAlign: "right", borderBottom: "1px solid #ddd" }}>Current Price</th>
            <th style={{ padding: "12px", textAlign: "right", borderBottom: "1px solid #ddd" }}>Market Value</th>
            <th style={{ padding: "12px", textAlign: "right", borderBottom: "1px solid #ddd" }}>Gain/Loss</th>
            <th style={{ padding: "12px", textAlign: "right", borderBottom: "1px solid #ddd" }}>%</th>
            <th style={{ padding: "12px", textAlign: "left", borderBottom: "1px solid #ddd" }}>Sector</th>
          </tr>
        </thead>
        <tbody>
          {data.holdings.map((holding, index) => (
            <tr key={index} style={{ borderBottom: "1px solid #eee" }}>
              <td style={{ padding: "12px", fontWeight: "bold" }}>{holding.symbol}</td>
              <td style={{ padding: "12px" }}>{holding.name}</td>
              <td style={{ padding: "12px", textAlign: "right" }}>{holding.quantity}</td>
              <td style={{ padding: "12px", textAlign: "right" }}>${holding.avgPrice.toFixed(2)}</td>
              <td style={{ padding: "12px", textAlign: "right" }}>${holding.currentPrice.toFixed(2)}</td>
              <td style={{ padding: "12px", textAlign: "right" }}>${holding.marketValue.toFixed(2)}</td>
              <td 
                style={{ 
                  padding: "12px", 
                  textAlign: "right", 
                  color: holding.gainLoss >= 0 ? "green" : "red",
                  fontWeight: "bold"
                }}
              >
                ${holding.gainLoss.toFixed(2)}
              </td>
              <td 
                style={{ 
                  padding: "12px", 
                  textAlign: "right", 
                  color: holding.gainLossPercent >= 0 ? "green" : "red"
                }}
              >
                {holding.gainLossPercent >= 0 ? "+" : ""}{holding.gainLossPercent.toFixed(2)}%
              </td>
              <td style={{ padding: "12px" }}>{holding.sector}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default HoldingsTable;
