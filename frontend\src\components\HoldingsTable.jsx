import React, { useEffect, useState } from "react";
import { getHoldings } from "../services/api";

const HoldingsTable = () => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    getHoldings()
      .then((res) => {
        setData(res.data);
        setLoading(false);
      })
      .catch((err) => {
        setError(err.message);
        setLoading(false);
      });
  }, []);

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(value);
  };

  const getSectorColor = (sector) => {
    const colors = {
      'Technology': '#3b82f6',
      'Healthcare': '#10b981',
      'Financial': '#f59e0b',
      'Consumer Staples': '#8b5cf6',
      'Energy': '#ef4444',
      'Industrials': '#06b6d4'
    };
    return colors[sector] || '#64748b';
  };

  if (loading) return (
    <div style={{
      padding: "3rem",
      textAlign: "center",
      color: "#64748b",
      fontSize: "1.125rem"
    }}>
      Loading holdings data...
    </div>
  );

  if (error) return (
    <div style={{
      padding: "2rem",
      textAlign: "center",
      color: "#ef4444",
      fontSize: "1.125rem",
      backgroundColor: "#fef2f2",
      borderRadius: "8px",
      border: "1px solid #fecaca"
    }}>
      Error loading holdings: {error}
    </div>
  );

  if (!data) return (
    <div style={{
      padding: "3rem",
      textAlign: "center",
      color: "#64748b",
      fontSize: "1.125rem"
    }}>
      No holdings data available
    </div>
  );

  return (
    <div style={{
      overflowX: "auto",
      borderRadius: "8px",
      border: "1px solid #e2e8f0"
    }}>
      <table style={{
        width: "100%",
        borderCollapse: "collapse",
        fontSize: "0.875rem"
      }}>
        <thead>
          <tr style={{
            backgroundColor: "#f8fafc",
            borderBottom: "2px solid #e2e8f0"
          }}>
            <th style={{
              padding: "1rem 1.5rem",
              textAlign: "left",
              fontWeight: "600",
              color: "#374151",
              fontSize: "0.75rem",
              textTransform: "uppercase",
              letterSpacing: "0.05em"
            }}>
              Stock
            </th>
            <th style={{
              padding: "1rem 1.5rem",
              textAlign: "right",
              fontWeight: "600",
              color: "#374151",
              fontSize: "0.75rem",
              textTransform: "uppercase",
              letterSpacing: "0.05em"
            }}>
              Quantity
            </th>
            <th style={{
              padding: "1rem 1.5rem",
              textAlign: "right",
              fontWeight: "600",
              color: "#374151",
              fontSize: "0.75rem",
              textTransform: "uppercase",
              letterSpacing: "0.05em"
            }}>
              Avg Price
            </th>
            <th style={{
              padding: "1rem 1.5rem",
              textAlign: "right",
              fontWeight: "600",
              color: "#374151",
              fontSize: "0.75rem",
              textTransform: "uppercase",
              letterSpacing: "0.05em"
            }}>
              Current Price
            </th>
            <th style={{
              padding: "1rem 1.5rem",
              textAlign: "right",
              fontWeight: "600",
              color: "#374151",
              fontSize: "0.75rem",
              textTransform: "uppercase",
              letterSpacing: "0.05em"
            }}>
              Market Value
            </th>
            <th style={{
              padding: "1rem 1.5rem",
              textAlign: "right",
              fontWeight: "600",
              color: "#374151",
              fontSize: "0.75rem",
              textTransform: "uppercase",
              letterSpacing: "0.05em"
            }}>
              Gain/Loss
            </th>
            <th style={{
              padding: "1rem 1.5rem",
              textAlign: "left",
              fontWeight: "600",
              color: "#374151",
              fontSize: "0.75rem",
              textTransform: "uppercase",
              letterSpacing: "0.05em"
            }}>
              Sector
            </th>
          </tr>
        </thead>
        <tbody>
          {data.holdings.map((holding, index) => {
            const isPositive = holding.gainLoss >= 0;
            return (
              <tr
                key={index}
                style={{
                  borderBottom: "1px solid #f1f5f9",
                  transition: "background-color 0.2s ease",
                  backgroundColor: index % 2 === 0 ? "#ffffff" : "#fafbfc"
                }}
                onMouseEnter={(e) => e.target.parentElement.style.backgroundColor = "#f8fafc"}
                onMouseLeave={(e) => e.target.parentElement.style.backgroundColor = index % 2 === 0 ? "#ffffff" : "#fafbfc"}
              >
                <td style={{ padding: "1.25rem 1.5rem" }}>
                  <div>
                    <div style={{
                      fontWeight: "600",
                      color: "#1e293b",
                      fontSize: "0.875rem",
                      marginBottom: "0.25rem"
                    }}>
                      {holding.symbol}
                    </div>
                    <div style={{
                      color: "#64748b",
                      fontSize: "0.75rem",
                      lineHeight: "1.2"
                    }}>
                      {holding.name}
                    </div>
                  </div>
                </td>
                <td style={{
                  padding: "1.25rem 1.5rem",
                  textAlign: "right",
                  fontWeight: "500",
                  color: "#374151"
                }}>
                  {holding.quantity.toLocaleString()}
                </td>
                <td style={{
                  padding: "1.25rem 1.5rem",
                  textAlign: "right",
                  color: "#64748b"
                }}>
                  {formatCurrency(holding.avgPrice)}
                </td>
                <td style={{
                  padding: "1.25rem 1.5rem",
                  textAlign: "right",
                  fontWeight: "500",
                  color: "#374151"
                }}>
                  {formatCurrency(holding.currentPrice)}
                </td>
                <td style={{
                  padding: "1.25rem 1.5rem",
                  textAlign: "right",
                  fontWeight: "600",
                  color: "#1e293b"
                }}>
                  {formatCurrency(holding.marketValue)}
                </td>
                <td style={{ padding: "1.25rem 1.5rem", textAlign: "right" }}>
                  <div>
                    <div style={{
                      color: isPositive ? "#10b981" : "#ef4444",
                      fontWeight: "600",
                      fontSize: "0.875rem"
                    }}>
                      {isPositive ? "+" : ""}{formatCurrency(holding.gainLoss)}
                    </div>
                    <div style={{
                      color: isPositive ? "#10b981" : "#ef4444",
                      fontSize: "0.75rem",
                      fontWeight: "500"
                    }}>
                      {isPositive ? "+" : ""}{holding.gainLossPercent.toFixed(2)}%
                    </div>
                  </div>
                </td>
                <td style={{ padding: "1.25rem 1.5rem" }}>
                  <span style={{
                    display: "inline-flex",
                    alignItems: "center",
                    padding: "0.25rem 0.75rem",
                    borderRadius: "9999px",
                    fontSize: "0.75rem",
                    fontWeight: "500",
                    backgroundColor: `${getSectorColor(holding.sector)}15`,
                    color: getSectorColor(holding.sector),
                    border: `1px solid ${getSectorColor(holding.sector)}30`
                  }}>
                    <div style={{
                      width: "6px",
                      height: "6px",
                      borderRadius: "50%",
                      backgroundColor: getSectorColor(holding.sector),
                      marginRight: "0.5rem"
                    }}></div>
                    {holding.sector}
                  </span>
                </td>
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
};

export default HoldingsTable;
