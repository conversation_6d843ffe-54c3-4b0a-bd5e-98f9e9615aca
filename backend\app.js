const express = require("express");
const cors = require("cors");

const holdings = require("./routes/holdings");
const allocation = require("./routes/allocation");
const performance = require("./routes/performance");
const summary = require("./routes/summary");

const app = express();
app.use(cors());
app.use(express.json());

app.use("/api/portfolio/holdings", holdings);
app.use("/api/portfolio/allocation", allocation);
app.use("/api/portfolio/performance", performance);
app.use("/api/portfolio/summary", summary);

const PORT = process.env.PORT || 5000;
app.listen(PORT, () => console.log(`Server running on port ${PORT}`));

