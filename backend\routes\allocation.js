const express = require("express");
const router = express.Router();
const holdings = require("../data/holdings.json");

router.get("/", (req, res) => {
  const sectorAllocation = {};
  
  holdings.holdings.forEach(holding => {
    if (sectorAllocation[holding.sector]) {
      sectorAllocation[holding.sector] += holding.marketValue;
    } else {
      sectorAllocation[holding.sector] = holding.marketValue;
    }
  });

  const totalValue = Object.values(sectorAllocation).reduce((sum, value) => sum + value, 0);
  
  const allocationData = Object.entries(sectorAllocation).map(([sector, value]) => ({
    sector,
    value,
    percentage: ((value / totalValue) * 100).toFixed(2)
  }));

  res.status(200).json({
    allocation: allocationData,
    totalValue
  });
});

module.exports = router; 